// Database service utilities for InsForge API

const BASE_URL = 'https://insforge-backend-740c116fd723.herokuapp.com/database'
const API_KEY = process.env.NEXT_PUBLIC_INSFORGE_API_KEY!

// Types based on database schema
export interface Post {
  id: string
  title: string
  content: string
  excerpt?: string
  author_id: string
  category_id: string
  pinned: boolean
  created_at: string
  updated_at?: string
}

export interface Comment {
  id: string
  content: string
  author_id: string
  post_id: string
  parent_comment_id?: string
  created_at: string
  updated_at?: string
}

export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  created_at: string
}

export interface Vote {
  id: string
  user_id: string
  post_id?: string
  comment_id?: string
  vote_type: 'upvote' | 'downvote'
  created_at: string
}

export interface User {
  id: string
  email: string
  name?: string
  created_at?: string
}

export interface DatabaseResponse<T> {
  data: T[]
  count: number
}

// Helper function to make API requests
const makeRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const response = await fetch(`${BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': API_KEY,
      ...options.headers,
    },
  })

  if (!response.ok) {
    const errorText = await response.text()
    throw new Error(`API Error: ${response.status} - ${errorText}`)
  }

  return response.json()
}

// Posts API
export const postsApi = {
  // Get all posts with optional filters
  getAll: async (params?: {
    limit?: number
    offset?: number
    order?: string
    category_id?: string
    author_id?: string
  }): Promise<DatabaseResponse<Post>> => {
    const searchParams = new URLSearchParams()
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.offset) searchParams.append('offset', params.offset.toString())
    if (params?.order) searchParams.append('order', params.order)
    if (params?.category_id) searchParams.append('category_id', params.category_id)
    if (params?.author_id) searchParams.append('author_id', params.author_id)

    const query = searchParams.toString()
    return makeRequest<DatabaseResponse<Post>>(`/tables/posts/records${query ? `?${query}` : ''}`)
  },

  // Get single post by ID
  getById: async (id: string): Promise<Post> => {
    const response = await makeRequest<DatabaseResponse<Post>>(`/tables/posts/records?id=${id}`)
    if (response.data.length === 0) {
      throw new Error('Post not found')
    }
    return response.data[0]
  },

  // Create new post
  create: async (post: Omit<Post, 'id' | 'created_at' | 'updated_at'>): Promise<Post[]> => {
    return makeRequest<Post[]>('/tables/posts/records', {
      method: 'POST',
      body: JSON.stringify([post]),
    })
  },

  // Update post
  update: async (id: string, updates: Partial<Omit<Post, 'id' | 'created_at'>>): Promise<Post> => {
    return makeRequest<Post>(`/tables/posts/records/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    })
  },

  // Delete post
  delete: async (id: string): Promise<void> => {
    await makeRequest(`/tables/posts/records/${id}`, {
      method: 'DELETE',
    })
  },
}

// Comments API
export const commentsApi = {
  // Get comments for a post
  getByPostId: async (postId: string, params?: {
    limit?: number
    offset?: number
    order?: string
  }): Promise<DatabaseResponse<Comment>> => {
    const searchParams = new URLSearchParams()
    searchParams.append('post_id', postId)
    if (params?.limit) searchParams.append('limit', params.limit.toString())
    if (params?.offset) searchParams.append('offset', params.offset.toString())
    if (params?.order) searchParams.append('order', params.order)

    const query = searchParams.toString()
    return makeRequest<DatabaseResponse<Comment>>(`/tables/comments/records?${query}`)
  },

  // Get replies to a comment
  getReplies: async (commentId: string): Promise<DatabaseResponse<Comment>> => {
    return makeRequest<DatabaseResponse<Comment>>(`/tables/comments/records?parent_comment_id=${commentId}`)
  },

  // Create new comment
  create: async (comment: Omit<Comment, 'id' | 'created_at' | 'updated_at'>): Promise<Comment[]> => {
    return makeRequest<Comment[]>('/tables/comments/records', {
      method: 'POST',
      body: JSON.stringify([comment]),
    })
  },

  // Update comment
  update: async (id: string, updates: Partial<Omit<Comment, 'id' | 'created_at'>>): Promise<Comment> => {
    return makeRequest<Comment>(`/tables/comments/records/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    })
  },

  // Delete comment
  delete: async (id: string): Promise<void> => {
    await makeRequest(`/tables/comments/records/${id}`, {
      method: 'DELETE',
    })
  },
}

// Categories API
export const categoriesApi = {
  // Get all categories
  getAll: async (): Promise<DatabaseResponse<Category>> => {
    return makeRequest<DatabaseResponse<Category>>('/tables/categories/records?order=name:asc')
  },

  // Get category by ID
  getById: async (id: string): Promise<Category> => {
    const response = await makeRequest<DatabaseResponse<Category>>(`/tables/categories/records?id=${id}`)
    if (response.data.length === 0) {
      throw new Error('Category not found')
    }
    return response.data[0]
  },

  // Get category by slug
  getBySlug: async (slug: string): Promise<Category> => {
    const response = await makeRequest<DatabaseResponse<Category>>(`/tables/categories/records?slug=${slug}`)
    if (response.data.length === 0) {
      throw new Error('Category not found')
    }
    return response.data[0]
  },
}

// Votes API
export const votesApi = {
  // Get votes for a post
  getPostVotes: async (postId: string): Promise<DatabaseResponse<Vote>> => {
    return makeRequest<DatabaseResponse<Vote>>(`/tables/votes/records?post_id=${postId}`)
  },

  // Get votes for a comment
  getCommentVotes: async (commentId: string): Promise<DatabaseResponse<Vote>> => {
    return makeRequest<DatabaseResponse<Vote>>(`/tables/votes/records?comment_id=${commentId}`)
  },

  // Get user's vote for a post
  getUserPostVote: async (userId: string, postId: string): Promise<Vote | null> => {
    const response = await makeRequest<DatabaseResponse<Vote>>(`/tables/votes/records?user_id=${userId}&post_id=${postId}`)
    return response.data.length > 0 ? response.data[0] : null
  },

  // Get user's vote for a comment
  getUserCommentVote: async (userId: string, commentId: string): Promise<Vote | null> => {
    const response = await makeRequest<DatabaseResponse<Vote>>(`/tables/votes/records?user_id=${userId}&comment_id=${commentId}`)
    return response.data.length > 0 ? response.data[0] : null
  },

  // Create or update vote
  vote: async (vote: Omit<Vote, 'id' | 'created_at'>): Promise<Vote[]> => {
    return makeRequest<Vote[]>('/tables/votes/records', {
      method: 'POST',
      body: JSON.stringify([vote]),
    })
  },

  // Delete vote
  delete: async (id: string): Promise<void> => {
    await makeRequest(`/tables/votes/records/${id}`, {
      method: 'DELETE',
    })
  },
}

// Users API (for getting user info)
export const usersApi = {
  // Get user by ID
  getById: async (id: string): Promise<User> => {
    const response = await makeRequest<DatabaseResponse<User>>(`/tables/users/records?id=${id}`)
    if (response.data.length === 0) {
      throw new Error('User not found')
    }
    return response.data[0]
  },

  // Get multiple users by IDs
  getByIds: async (ids: string[]): Promise<DatabaseResponse<User>> => {
    // Note: This might need to be implemented differently depending on API capabilities
    // For now, we'll make individual requests
    const users = await Promise.all(ids.map(id => usersApi.getById(id).catch(() => null)))
    const validUsers = users.filter(user => user !== null) as User[]
    return {
      data: validUsers,
      count: validUsers.length
    }
  },
}

// Utility functions
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`
  
  return date.toLocaleDateString()
}

export const generateExcerpt = (content: string, maxLength: number = 150): string => {
  if (content.length <= maxLength) return content
  return content.substring(0, maxLength).trim() + '...'
}
