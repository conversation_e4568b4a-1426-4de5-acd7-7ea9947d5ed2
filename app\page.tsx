"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowUp,
  ArrowDown,
  Bell,
  Bookmark,
  Compass,
  Home,
  LogIn,
  LogOut,
  MessageSquare,
  Plus,
  Search,
  TrendingUp,
  Users,
  User,
  Loader2,
} from "lucide-react"

// Import the Footer component
import { Footer } from "@/components/footer"
import { useAuth } from "@/contexts/auth-context"
import {
  postsApi,
  categoriesApi,
  usersApi,
  votesApi,
  commentsApi,
  formatDate,
  type Post,
  type Category,
  type User as DbUser
} from "@/lib/database-service"

export default function HomePage() {
  const [activeTab, setActiveTab] = useState("all")
  const [posts, setPosts] = useState<Post[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [users, setUsers] = useState<Record<string, DbUser>>({})
  const [postVotes, setPostVotes] = useState<Record<string, { upvotes: number; downvotes: number; userVote?: 'upvote' | 'downvote' }>>({})
  const [commentCounts, setCommentCounts] = useState<Record<string, number>>({})

  // Loading states
  const [isLoadingPosts, setIsLoadingPosts] = useState(true)
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)
  const [showMorePosts, setShowMorePosts] = useState(false)
  const [currentPage, setCurrentPage] = useState(0)
  const [hasMorePosts, setHasMorePosts] = useState(true)

  // Get authentication state
  const { user, isAuthenticated, logout, isLoading } = useAuth()

  // Load posts from database
  const loadPosts = async (page = 0, append = false) => {
    try {
      setIsLoadingPosts(true)
      const limit = 10
      const offset = page * limit

      const response = await postsApi.getAll({
        limit,
        offset,
        order: 'created_at:desc'
      })

      const newPosts = response.data
      setPosts(prev => append ? [...prev, ...newPosts] : newPosts)
      setHasMorePosts(newPosts.length === limit)

      // Load user data for posts
      const authorIds = [...new Set(newPosts.map(post => post.author_id))]
      await loadUsers(authorIds)

      // Load vote counts and comment counts for posts
      await Promise.all([
        loadPostVotes(newPosts.map(post => post.id)),
        loadCommentCounts(newPosts.map(post => post.id))
      ])

    } catch (error) {
      console.error('Failed to load posts:', error)
    } finally {
      setIsLoadingPosts(false)
    }
  }

  // Load categories from database
  const loadCategories = async () => {
    try {
      setIsLoadingCategories(true)
      const response = await categoriesApi.getAll()
      setCategories(response.data)
    } catch (error) {
      console.error('Failed to load categories:', error)
    } finally {
      setIsLoadingCategories(false)
    }
  }

  // Load user data
  const loadUsers = async (userIds: string[]) => {
    try {
      const newUserIds = userIds.filter(id => !users[id])
      if (newUserIds.length === 0) return

      const userPromises = newUserIds.map(id =>
        usersApi.getById(id).catch(() => null)
      )
      const userData = await Promise.all(userPromises)

      const userMap: Record<string, DbUser> = {}
      userData.forEach((user, index) => {
        if (user) {
          userMap[newUserIds[index]] = user
        }
      })

      setUsers(prev => ({ ...prev, ...userMap }))
    } catch (error) {
      console.error('Failed to load users:', error)
    }
  }

  // Load vote counts for posts
  const loadPostVotes = async (postIds: string[]) => {
    try {
      const votePromises = postIds.map(async (postId) => {
        const votes = await votesApi.getPostVotes(postId)
        const upvotes = votes.data.filter(vote => vote.vote_type === 'upvote').length
        const downvotes = votes.data.filter(vote => vote.vote_type === 'downvote').length

        let userVote: 'upvote' | 'downvote' | undefined
        if (user) {
          const userVoteData = votes.data.find(vote => vote.user_id === user.user_id)
          userVote = userVoteData?.vote_type as 'upvote' | 'downvote' | undefined
        }

        return { postId, upvotes, downvotes, userVote }
      })

      const voteResults = await Promise.all(votePromises)
      const voteMap: Record<string, { upvotes: number; downvotes: number; userVote?: 'upvote' | 'downvote' }> = {}

      voteResults.forEach(({ postId, upvotes, downvotes, userVote }) => {
        voteMap[postId] = { upvotes, downvotes, userVote }
      })

      setPostVotes(prev => ({ ...prev, ...voteMap }))
    } catch (error) {
      console.error('Failed to load post votes:', error)
    }
  }

  // Load comment counts for posts
  const loadCommentCounts = async (postIds: string[]) => {
    try {
      const countPromises = postIds.map(async (postId) => {
        const comments = await commentsApi.getByPostId(postId, { limit: 1000 })
        return { postId, count: comments.count }
      })

      const countResults = await Promise.all(countPromises)
      const countMap: Record<string, number> = {}

      countResults.forEach(({ postId, count }) => {
        countMap[postId] = count
      })

      setCommentCounts(prev => ({ ...prev, ...countMap }))
    } catch (error) {
      console.error('Failed to load comment counts:', error)
    }
  }

  // Handle voting
  const handleVote = async (postId: string, voteType: 'upvote' | 'downvote') => {
    if (!user) return

    try {
      const currentVote = postVotes[postId]?.userVote

      // If user already voted the same way, remove the vote
      if (currentVote === voteType) {
        // Find and delete the existing vote
        const existingVotes = await votesApi.getPostVotes(postId)
        const userVote = existingVotes.data.find(vote => vote.user_id === user.user_id)
        if (userVote) {
          await votesApi.delete(userVote.id)
        }

        // Update local state
        setPostVotes(prev => ({
          ...prev,
          [postId]: {
            ...prev[postId],
            [voteType === 'upvote' ? 'upvotes' : 'downvotes']: Math.max(0, (prev[postId]?.[voteType === 'upvote' ? 'upvotes' : 'downvotes'] || 0) - 1),
            userVote: undefined
          }
        }))
      } else {
        // Create new vote or update existing
        await votesApi.vote({
          user_id: user.user_id,
          post_id: postId,
          vote_type: voteType
        })

        // Update local state
        setPostVotes(prev => {
          const current = prev[postId] || { upvotes: 0, downvotes: 0 }
          const newVotes = { ...current }

          // Remove previous vote if exists
          if (currentVote) {
            newVotes[currentVote === 'upvote' ? 'upvotes' : 'downvotes'] = Math.max(0, newVotes[currentVote === 'upvote' ? 'upvotes' : 'downvotes'] - 1)
          }

          // Add new vote
          newVotes[voteType === 'upvote' ? 'upvotes' : 'downvotes'] += 1
          newVotes.userVote = voteType

          return { ...prev, [postId]: newVotes }
        })
      }
    } catch (error) {
      console.error('Failed to vote:', error)
    }
  }

  const handleUpvote = (id: string) => handleVote(id, 'upvote')
  const handleDownvote = (id: string) => handleVote(id, 'downvote')

  // Load data on component mount
  useEffect(() => {
    loadPosts()
    loadCategories()
  }, [])

  // Reload votes when user authentication changes
  useEffect(() => {
    if (posts.length > 0) {
      loadPostVotes(posts.map(post => post.id))
    }
  }, [user, posts.length])

  // Handle load more posts
  const handleLoadMore = () => {
    if (!showMorePosts && hasMorePosts) {
      setCurrentPage(prev => prev + 1)
      loadPosts(currentPage + 1, true)
      setShowMorePosts(true)
    } else {
      setShowMorePosts(false)
    }
  }

  // Mock data for categories (keeping for fallback)
  const mockCategories = [
    {
      id: "general",
      name: "General Discussion",
      description: "Talk about anything related to our community",
      threads: 124,
      posts: 1453,
      icon: <MessageSquare className="h-5 w-5" />,
    },
    {
      id: "introductions",
      name: "Introductions",
      description: "New to the forum? Introduce yourself here!",
      threads: 87,
      posts: 342,
      icon: <Users className="h-5 w-5" />,
    },
    {
      id: "announcements",
      name: "Announcements",
      description: "Important updates and announcements",
      threads: 32,
      posts: 156,
      icon: <TrendingUp className="h-5 w-5" />,
    },
  ]

  // Mock data for all discussions
  const allDiscussions = [
    {
      id: "thread-1",
      title: "Welcome to GenVibe!",
      category: "announcements",
      categoryName: "Announcements",
      author: "Sarah",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "2 days ago",
      replies: 24,
      pinned: true,
      excerpt: "This is a welcome thread for all new members. Please read the rules before posting.",
    },
    {
      id: "thread-2",
      title: "How do I get started with Next.js?",
      category: "general",
      categoryName: "General Discussion",
      author: "Alex",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "1 day ago",
      replies: 15,
      pinned: false,
      excerpt: "I'm new to Next.js and would like some guidance on how to get started with my first project.",
    },
    {
      id: "thread-3",
      title: "Sharing my experience with Vercel deployment",
      category: "general",
      categoryName: "General Discussion",
      author: "Michael",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "12 hours ago",
      replies: 8,
      pinned: false,
      excerpt: "I recently deployed my application to Vercel and wanted to share my experience with the community.",
    },
    {
      id: "thread-4",
      title: "Introducing myself to the community",
      category: "introductions",
      categoryName: "Introductions",
      author: "Jessica",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "3 hours ago",
      replies: 5,
      pinned: false,
      excerpt:
        "Hello everyone! I'm new here and excited to be part of this community. I'm a frontend developer with 2 years of experience.",
    },
    {
      id: "thread-5",
      title: "Important changes to our community guidelines",
      category: "announcements",
      categoryName: "Announcements",
      author: "David",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "1 day ago",
      replies: 19,
      pinned: true,
      excerpt:
        "We've updated our community guidelines to ensure a better experience for everyone. Please take a moment to review them.",
    },
  ]

  // Add additional discussions that will show when "Load More" is clicked
  const additionalDiscussions = [
    {
      id: "thread-6",
      title: "The ultimate guide to creating viral TikTok content",
      category: "social-media",
      categoryName: "Social Media",
      author: "TikTokCreator",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "5 hours ago",
      replies: 37,
      pinned: false,
      excerpt:
        "Want to go viral on TikTok? I've analyzed hundreds of trending videos and discovered these key patterns that can help boost your content.",
    },
    {
      id: "thread-7",
      title: "Is BeReal actually keeping it real? Let's discuss",
      category: "social-media",
      categoryName: "Social Media",
      author: "AuthenticVibes",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "1 day ago",
      replies: 42,
      pinned: false,
      excerpt:
        "BeReal promises authenticity, but are people really showing their unfiltered lives? Let's talk about the reality behind the app.",
    },
    {
      id: "thread-8",
      title: "Sustainable fashion brands that don't break the bank",
      category: "lifestyle",
      categoryName: "Lifestyle",
      author: "EcoFashionista",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      date: "2 days ago",
      replies: 29,
      pinned: false,
      excerpt:
        "Looking for sustainable fashion that's actually affordable? I've compiled a list of brands that are both eco-friendly and budget-conscious.",
    },
  ]

  // Update the trending topics with more Gen Z relevant topics
  const trendingTopics = [
    "BeReal",
    "Spotify Wrapped",
    "Sustainable Fashion",
    "AI Tools",
    "Mental Health",
    "Side Hustles",
    "TikTok",
  ]

  // Modify the handleLoadMore function
  const handleLoadMore = () => {
    setShowMorePosts(true)
  }

  // Mock data for popular posts
  const popularPosts = [
    {
      id: "popular-1",
      title: "How I built a 6-figure business on Etsy while in college",
      author: "SideHustleQueen",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      upvotes: 156,
      replies: 42,
      date: "3 days ago",
    },
    {
      id: "popular-2",
      title: "The truth about 'That Girl' morning routines nobody talks about",
      author: "RealityCheck",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      upvotes: 124,
      replies: 37,
      date: "2 days ago",
    },
    {
      id: "popular-3",
      title: "I deleted all social media for 30 days. Here's what happened to my mental health",
      author: "DigitalDetoxer",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      upvotes: 98,
      replies: 29,
      date: "1 day ago",
    },
    {
      id: "popular-4",
      title: "Thrifting hacks that helped me build my dream wardrobe for under $200",
      author: "ThriftFlip",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      upvotes: 87,
      replies: 31,
      date: "4 days ago",
    },
  ]

  // Mock data for bookmarks
  const bookmarks = [
    {
      id: "bookmark-1",
      title: "Complete guide to Next.js App Router",
      author: "NextExpert",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      category: "Tutorials",
      date: "Saved 2 days ago",
    },
    {
      id: "bookmark-2",
      title: "Understanding React Server Components",
      author: "ReactDev",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      category: "Guides",
      date: "Saved 5 days ago",
    },
  ]

  // Mock data for notifications
  const notifications = [
    {
      id: "notif-1",
      type: "reply",
      author: "Emma",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      content: "replied to your post",
      thread: "How do I get started with Next.js?",
      time: "2 hours ago",
      read: false,
    },
    {
      id: "notif-2",
      type: "mention",
      author: "Daniel",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      content: "mentioned you in a comment",
      thread: "The future of web development",
      time: "1 day ago",
      read: true,
    },
    {
      id: "notif-3",
      type: "upvote",
      author: "Alex",
      authorAvatar: "/placeholder.svg?height=40&width=40",
      content: "upvoted your post",
      thread: "Sharing my experience with Vercel deployment",
      time: "3 days ago",
      read: true,
    },
  ]

  return (
    <div className="flex min-h-screen bg-[#1e2a36] text-white">
      {/* Sidebar */}
      <div className="hidden md:flex w-64 flex-col bg-[#1e2a36]/80 backdrop-blur-sm border-r border-[#2a3a4a]">
        <div className="p-4 border-b border-[#2a3a4a] flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-2">
            <Image src="/images/genvibe-logo.png" alt="GenVibe Logo" width={40} height={40} className="h-8 w-auto" />
            <span className="text-xl font-bold text-white">GenVibe</span>
          </Link>
        </div>

        {/* Trending topics at the top */}
        <div className="p-4 border-b border-[#2a3a4a]">
          <h3 className="text-xs font-medium uppercase text-[#F2C1AE] tracking-wider mb-2">Trending</h3>
          <div className="flex flex-wrap gap-2">
            {trendingTopics.map((topic) => (
              <Link
                key={topic}
                href={`/topic/${topic.toLowerCase().replace(/\s+/g, "-")}`}
                className="text-xs px-2 py-1 rounded-full bg-[#2a3a4a] text-white hover:bg-[#F24F13]/20 hover:text-white transition-colors"
              >
                {topic}
              </Link>
            ))}
          </div>
        </div>

        <div className="p-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-[#F2C1AE]/50" />
            <Input
              type="search"
              placeholder="Search..."
              className="w-full bg-[#2a3a4a]/80 border-[#2a3a4a] pl-9 text-sm text-[#F2C1AE] placeholder:text-[#F2C1AE]/50 focus:border-[#F26430]"
            />
          </div>
        </div>

        <nav className="flex-1 p-4 space-y-1">
          <Link href="/" className="flex items-center space-x-2 px-3 py-2 rounded-md bg-[#F24F13]/20 text-[#F2C1AE]">
            <Home className="h-4 w-4" />
            <span>Home</span>
          </Link>
          <button
            onClick={() => setActiveTab("popular")}
            className={`w-full flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors ${activeTab === "popular" ? "bg-[#F24F13]/10 text-[#F2C1AE]" : ""}`}
          >
            <TrendingUp className="h-4 w-4" />
            <span>Popular</span>
          </button>
          <button
            onClick={() => setActiveTab("explore")}
            className={`w-full flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors ${activeTab === "explore" ? "bg-[#F24F13]/10 text-[#F2C1AE]" : ""}`}
          >
            <Compass className="h-4 w-4" />
            <span>Explore</span>
          </button>
          <button
            onClick={() => setActiveTab("bookmarks")}
            className={`w-full flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors ${activeTab === "bookmarks" ? "bg-[#F24F13]/10 text-[#F2C1AE]" : ""}`}
          >
            <Bookmark className="h-4 w-4" />
            <span>Bookmarks</span>
          </button>
          <button
            onClick={() => setActiveTab("notifications")}
            className={`w-full flex items-center space-x-2 px-3 py-2 rounded-md text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#F24F13]/10 transition-colors ${activeTab === "notifications" ? "bg-[#F24F13]/10 text-[#F2C1AE]" : ""}`}
          >
            <Bell className="h-4 w-4" />
            <span>Notifications</span>
            <Badge className="ml-auto bg-[#F24F13] text-white text-xs">3</Badge>
          </button>
        </nav>

        <div className="p-4 mt-auto border-t border-[#2a3a4a]">
          {isAuthenticated ? (
            <Link href="/create-post">
              <Button className="w-full bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white">
                <Plus className="h-4 w-4 mr-2" />
                New Post
              </Button>
            </Link>
          ) : (
            <Link href="/login">
              <Button className="w-full bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white">
                <Plus className="h-4 w-4 mr-2" />
                Login to Post
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-auto">
        <header className="sticky top-0 z-10 bg-[#1e2a36]/80 backdrop-blur-md border-b border-[#2a3a4a] p-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            {/* Update the mobile header logo to include text */}
            <div className="md:hidden flex items-center">
              <Image src="/images/genvibe-logo.png" alt="GenVibe Logo" width={32} height={32} className="h-7 w-auto" />
              <span className="ml-2 text-lg font-bold text-white">GenVibe</span>
            </div>
            <div className="relative hidden sm:block max-w-md w-full">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-[#F2C1AE]/50" />
              <Input
                type="search"
                placeholder="Search discussions..."
                className="w-full bg-[#2a3a4a]/80 border-[#2a3a4a] pl-9 text-sm text-[#F2C1AE] placeholder:text-[#F2C1AE]/50 focus:border-[#F26430]"
              />
            </div>
          </div>
          <div className="flex space-x-2 items-center">
            <Button
              variant="outline"
              className="md:hidden border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
            >
              <Search className="h-4 w-4" />
            </Button>

            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 rounded-full bg-[#2a3a4a] animate-pulse"></div>
              </div>
            ) : isAuthenticated && user ? (
              <div className="flex items-center space-x-2">
                <span className="hidden sm:inline text-sm text-[#F2C1AE]/70">
                  Welcome, {user.name}
                </span>
                <Link href="/profile">
                  <Avatar className="h-8 w-8 border border-[#2a3a4a] cursor-pointer hover:border-[#F26430] transition-colors">
                    <AvatarFallback className="text-xs bg-[#F26430] text-white">
                      {user.name[0].toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </Link>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                  className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Logout</span>
                </Button>
              </div>
            ) : (
              <Link href="/login">
                <Button
                  variant="outline"
                  className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
                >
                  <LogIn className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Login</span>
                </Button>
              </Link>
            )}
          </div>
        </header>

        <main className="p-4 md:p-6 max-w-5xl mx-auto">
          {/* Mobile trending topics */}
          <div className="md:hidden mb-6 overflow-x-auto pb-2">
            <div className="flex gap-2 w-max">
              {trendingTopics.map((topic) => (
                <Link
                  key={topic}
                  href={`/topic/${topic.toLowerCase().replace(/\s+/g, "-")}`}
                  className="text-xs px-2 py-1 rounded-full bg-[#2a3a4a] text-white hover:bg-[#F24F13]/20 hover:text-white transition-colors whitespace-nowrap"
                >
                  {topic}
                </Link>
              ))}
            </div>
          </div>

          {/* Mobile navigation */}
          <div className="md:hidden mb-6 flex overflow-x-auto space-x-2 pb-2">
            <Button
              variant={activeTab === "all" ? "default" : "outline"}
              onClick={() => setActiveTab("all")}
              className={activeTab === "all" ? "bg-[#F24F13] text-white" : "border-[#2a3a4a] text-[#F2C1AE]/80"}
              size="sm"
            >
              <Home className="h-4 w-4 mr-2" />
              Home
            </Button>
            <Button
              variant={activeTab === "popular" ? "default" : "outline"}
              onClick={() => setActiveTab("popular")}
              className={activeTab === "popular" ? "bg-[#F24F13] text-white" : "border-[#2a3a4a] text-[#F2C1AE]/80"}
              size="sm"
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Popular
            </Button>
            <Button
              variant={activeTab === "explore" ? "default" : "outline"}
              onClick={() => setActiveTab("explore")}
              className={activeTab === "explore" ? "bg-[#F24F13] text-white" : "border-[#2a3a4a] text-[#F2C1AE]/80"}
              size="sm"
            >
              <Compass className="h-4 w-4 mr-2" />
              Explore
            </Button>
            <Button
              variant={activeTab === "bookmarks" ? "default" : "outline"}
              onClick={() => setActiveTab("bookmarks")}
              className={activeTab === "bookmarks" ? "bg-[#F24F13] text-white" : "border-[#2a3a4a] text-[#F2C1AE]/80"}
              size="sm"
            >
              <Bookmark className="h-4 w-4 mr-2" />
              Saved
            </Button>
            <Button
              variant={activeTab === "notifications" ? "default" : "outline"}
              onClick={() => setActiveTab("notifications")}
              className={
                activeTab === "notifications" ? "bg-[#F24F13] text-white" : "border-[#2a3a4a] text-[#F2C1AE]/80"
              }
              size="sm"
            >
              <Bell className="h-4 w-4 mr-2" />
              Alerts
            </Button>
          </div>

          {/* Home Tab */}
          {(activeTab === "all" || activeTab === "home") && (
            <Tabs defaultValue="all" className="mb-8">
              <TabsList className="bg-[#2a3a4a]/80 p-1 mb-6">
                <TabsTrigger value="all" className="data-[state=active]:bg-[#F24F13] data-[state=active]:text-white">
                  All Discussions
                </TabsTrigger>
                <TabsTrigger
                  value="categories"
                  className="data-[state=active]:bg-[#F24F13] data-[state=active]:text-white"
                >
                  Categories
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="space-y-6">
                {isLoadingPosts ? (
                  <div className="flex justify-center items-center py-12">
                    <div className="flex flex-col items-center space-y-4">
                      <Loader2 className="h-8 w-8 animate-spin text-[#F26430]" />
                      <p className="text-[#F2C1AE]/70">Loading posts...</p>
                    </div>
                  </div>
                ) : posts.length === 0 ? (
                  <div className="text-center py-12">
                    <MessageSquare className="h-12 w-12 mx-auto text-[#F2C1AE]/50 mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">No posts yet</h3>
                    <p className="text-[#F2C1AE]/70 mb-4">Be the first to start a discussion!</p>
                    {isAuthenticated && (
                      <Link href="/create-post">
                        <Button className="bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white">
                          <Plus className="h-4 w-4 mr-2" />
                          Create First Post
                        </Button>
                      </Link>
                    )}
                  </div>
                ) : (
                  <div className="grid gap-4">
                    {posts.map((post) => {
                      const author = users[post.author_id]
                      const category = categories.find(cat => cat.id === post.category_id)
                      const votes = postVotes[post.id] || { upvotes: 0, downvotes: 0 }
                      const commentCount = commentCounts[post.id] || 0

                      return (
                        <Card
                          key={post.id}
                          className="bg-[#2a3a4a]/60 border-[#2a3a4a] hover:border-[#F26430]/50 transition-colors overflow-hidden"
                        >
                          <div className="flex">
                            {/* Voting */}
                            <div className="flex flex-col items-center p-4 border-r border-[#2a3a4a] text-[#F2C1AE]/70">
                              <button
                                className={`hover:text-[#F24F13] transition-colors ${votes.userVote === 'upvote' ? 'text-[#F24F13]' : ''}`}
                                onClick={() => handleUpvote(post.id)}
                                disabled={!isAuthenticated}
                              >
                                <ArrowUp className="h-5 w-5" />
                              </button>
                              <span className="my-1 font-medium">{votes.upvotes - votes.downvotes}</span>
                              <button
                                className={`hover:text-[#F26430] transition-colors ${votes.userVote === 'downvote' ? 'text-[#F26430]' : ''}`}
                                onClick={() => handleDownvote(post.id)}
                                disabled={!isAuthenticated}
                              >
                                <ArrowDown className="h-5 w-5" />
                              </button>
                            </div>

                            {/* Content */}
                            <div className="flex-1">
                              <CardHeader className="pb-2">
                                <div className="flex flex-wrap items-center gap-2 mb-1">
                                  {post.pinned && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs font-normal border-[#2a3a4a] text-[#F2C1AE]/70 px-1.5 py-0"
                                    >
                                      Pinned
                                    </Badge>
                                  )}
                                  {category && (
                                    <Badge className="text-xs font-normal px-1.5 py-0 bg-[#F26430]/20 text-[#F26430] hover:bg-[#F26430]/30">
                                      {category.name}
                                    </Badge>
                                  )}
                                </div>

                                <Link
                                  href={`/thread/${post.id}`}
                                  className="text-lg font-medium hover:underline line-clamp-1 text-white"
                                >
                                  {post.title}
                                </Link>

                                <div className="flex items-center gap-2 mt-1 text-sm text-[#F2C1AE]/70">
                                  <Avatar className="h-5 w-5 border border-[#2a3a4a]">
                                    <AvatarFallback className="text-xs bg-[#F26430] text-white">
                                      {author?.name?.[0]?.toUpperCase() || 'U'}
                                    </AvatarFallback>
                                  </Avatar>
                                  <span>{author?.name || 'Unknown User'}</span>
                                  <span>•</span>
                                  <span>{formatDate(post.created_at)}</span>
                                </div>
                              </CardHeader>
                              <CardContent>
                                <p className="text-sm text-[#F2C1AE] line-clamp-2">{post.excerpt}</p>
                                <div className="flex items-center mt-4 text-sm text-[#F2C1AE]/50">
                                  <MessageSquare className="h-4 w-4 mr-1" />
                                  <span>{commentCount} comments</span>
                                </div>
                              </CardContent>
                            </div>
                          </div>
                        </Card>
                      )
                    })}
                  </div>
                )}

                {hasMorePosts && (
                  <div className="flex justify-center">
                    <Button
                      variant="outline"
                      className="border-[#2a3a4a] text-[#F2C1AE]/70 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
                      onClick={handleLoadMore}
                      disabled={isLoadingPosts}
                    >
                      {isLoadingPosts ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        "Load More"
                      )}
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="categories" className="space-y-6">
                {isLoadingCategories ? (
                  <div className="flex justify-center items-center py-12">
                    <div className="flex flex-col items-center space-y-4">
                      <Loader2 className="h-8 w-8 animate-spin text-[#F26430]" />
                      <p className="text-[#F2C1AE]/70">Loading categories...</p>
                    </div>
                  </div>
                ) : (
                  <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {categories.map((category) => {
                      const categoryPosts = posts.filter(post => post.category_id === category.id)
                      const postCount = categoryPosts.length

                      return (
                        <Card
                          key={category.id}
                          className="bg-[#2a3a4a]/60 border-[#2a3a4a] hover:border-[#F26430]/50 transition-colors h-full"
                        >
                          <CardHeader className="flex flex-row items-center gap-4 pb-2">
                            <div className="rounded-md p-2 bg-[#F26430]/20 text-[#F26430]">
                              <MessageSquare className="h-5 w-5" />
                            </div>
                            <div className="space-y-1">
                              <Link
                                href={`/category/${category.slug}`}
                                className="text-lg font-medium hover:underline text-white"
                              >
                                {category.name}
                              </Link>
                              <p className="text-[#F2C1AE]/70 text-sm">{category.description || 'No description available'}</p>
                            </div>
                          </CardHeader>
                          <CardContent className="pb-4">
                            <div className="flex items-center text-sm text-[#F2C1AE]/50">
                              <span>{postCount} posts</span>
                            </div>
                            <Link
                              href={`/category/${category.slug}`}
                              className="mt-4 inline-block text-sm text-[#F24F13] hover:text-[#F26430] transition-colors"
                            >
                              Browse category →
                            </Link>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          )}

          {/* Popular Tab */}
          {activeTab === "popular" && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-white">Popular Discussions</h2>
              <div className="grid gap-4">
                {popularPosts.map((post) => (
                  <Card
                    key={post.id}
                    className="bg-[#2a3a4a]/60 border-[#2a3a4a] hover:border-[#F26430]/50 transition-colors overflow-hidden"
                  >
                    <div className="flex">
                      {/* Voting */}
                      <div className="flex flex-col items-center p-4 border-r border-[#2a3a4a] text-[#F2C1AE]/70">
                        <button className="hover:text-[#F24F13] transition-colors">
                          <ArrowUp className="h-5 w-5" />
                        </button>
                        <span className="my-1 font-medium">{post.upvotes}</span>
                        <button className="hover:text-[#F26430] transition-colors">
                          <ArrowDown className="h-5 w-5" />
                        </button>
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        <CardHeader className="pb-2">
                          <Link
                            href={`/thread/${post.id}`}
                            className="text-lg font-medium hover:underline line-clamp-1 text-white"
                          >
                            {post.title}
                          </Link>

                          <div className="flex items-center gap-2 mt-1 text-sm text-[#F2C1AE]/70">
                            <Avatar className="h-5 w-5 border border-[#2a3a4a]">
                              <AvatarImage src={post.authorAvatar} alt={post.author} />
                              <AvatarFallback className="text-xs bg-[#F26430] text-white">
                                {post.author[0].toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span>{post.author}</span>
                            <span>•</span>
                            <span>{post.date}</span>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center mt-2 text-sm text-[#F2C1AE]/50">
                            <Badge className="bg-[#F24F13]/20 text-[#F24F13] mr-2">Trending</Badge>
                            <MessageSquare className="h-4 w-4 mr-1" />
                            <span>{post.replies} comments</span>
                          </div>
                        </CardContent>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              <div className="flex justify-center">
                <Button
                  variant="outline"
                  className="border-[#2a3a4a] text-[#F2C1AE]/70 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
                >
                  Load More
                </Button>
              </div>
            </div>
          )}

          {/* Explore Tab */}
          {activeTab === "explore" && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-white">Explore Topics</h2>

              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                <Card className="bg-[#2a3a4a]/60 border-[#2a3a4a] hover:border-[#F26430]/50 transition-colors">
                  <CardHeader className="pb-2">
                    <h3 className="text-lg font-medium text-white">Side Hustles</h3>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-[#F2C1AE]/70 mb-4">Discussions about making money on your own terms.</p>
                    <div className="flex flex-wrap gap-2">
                      <Badge className="bg-[#F24F13]/20 text-[#F24F13]">Etsy</Badge>
                      <Badge className="bg-[#F26430]/20 text-[#F26430]">Dropshipping</Badge>
                      <Badge className="bg-[#F2865E]/20 text-[#F2865E]">Content Creation</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-[#2a3a4a]/60 border-[#2a3a4a] hover:border-[#F26430]/50 transition-colors">
                  <CardHeader className="pb-2">
                    <h3 className="text-lg font-medium text-white">Sustainable Living</h3>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-[#F2C1AE]/70 mb-4">
                      Discussions about eco-friendly lifestyles and choices.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <Badge className="bg-[#F24F13]/20 text-[#F24F13]">Thrifting</Badge>
                      <Badge className="bg-[#F26430]/20 text-[#F26430]">Zero Waste</Badge>
                      <Badge className="bg-[#F2865E]/20 text-[#F2865E]">Minimalism</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-[#2a3a4a]/60 border-[#2a3a4a] hover:border-[#F26430]/50 transition-colors">
                  <CardHeader className="pb-2">
                    <h3 className="text-lg font-medium text-white">Digital Wellness</h3>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-[#F2C1AE]/70 mb-4">
                      Discussions about healthy relationships with technology.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <Badge className="bg-[#F24F13]/20 text-[#F24F13]">Digital Detox</Badge>
                      <Badge className="bg-[#F26430]/20 text-[#F26430]">Mindfulness</Badge>
                      <Badge className="bg-[#F2865E]/20 text-[#F2865E]">Screen Time</Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex justify-center">
                <Button
                  variant="outline"
                  className="border-[#2a3a4a] text-[#F2C1AE]/70 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
                >
                  Explore More
                </Button>
              </div>
            </div>
          )}

          {/* Bookmarks Tab */}
          {activeTab === "bookmarks" && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-white">Your Bookmarks</h2>

              <div className="grid gap-4">
                {bookmarks.map((bookmark) => (
                  <Card
                    key={bookmark.id}
                    className="bg-[#2a3a4a]/60 border-[#2a3a4a] hover:border-[#F26430]/50 transition-colors"
                  >
                    <CardHeader className="pb-2">
                      <Badge className="w-fit mb-2 bg-[#F26430]/20 text-[#F26430]">{bookmark.category}</Badge>
                      <Link href={`/thread/${bookmark.id}`} className="text-lg font-medium hover:underline text-white">
                        {bookmark.title}
                      </Link>

                      <div className="flex items-center gap-2 mt-1 text-sm text-[#F2C1AE]/70">
                        <Avatar className="h-5 w-5 border border-[#2a3a4a]">
                          <AvatarImage src={bookmark.authorAvatar} alt={bookmark.author} />
                          <AvatarFallback className="text-xs bg-[#F26430] text-white">
                            {bookmark.author[0].toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span>{bookmark.author}</span>
                        <span>•</span>
                        <span>{bookmark.date}</span>
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>

              {bookmarks.length === 0 ? (
                <div className="text-center py-12">
                  <Bookmark className="h-12 w-12 mx-auto text-[#F2C1AE]/30 mb-4" />
                  <h3 className="text-lg font-medium text-white mb-2">No bookmarks yet</h3>
                  <p className="text-[#F2C1AE]/70 mb-4">Save posts to read them later</p>
                  <Button className="bg-[#F24F13] hover:bg-[#F26430] text-white">Browse Discussions</Button>
                </div>
              ) : null}
            </div>
          )}

          {/* Notifications Tab */}
          {activeTab === "notifications" && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-white">Notifications</h2>
                <Button
                  variant="outline"
                  className="text-xs border-[#2a3a4a] text-[#F2C1AE]/70 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
                >
                  Mark all as read
                </Button>
              </div>

              <div className="grid gap-3">
                {notifications.map((notification) => (
                  <Card
                    key={notification.id}
                    className={`bg-[#2a3a4a]/60 border-[#2a3a4a] hover:border-[#F26430]/50 transition-colors ${!notification.read ? "border-l-4 border-l-[#F24F13]" : ""}`}
                  >
                    <div className="p-4 flex items-start gap-3">
                      <Avatar className="h-8 w-8 border border-[#2a3a4a]">
                        <AvatarImage src={notification.authorAvatar} alt={notification.author} />
                        <AvatarFallback className="bg-[#F26430] text-white">
                          {notification.author[0].toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-[#F2C1AE]">
                          <span className="font-medium text-white">{notification.author}</span> {notification.content}
                        </p>
                        <Link href="#" className="text-sm text-[#F24F13] hover:underline truncate block">
                          {notification.thread}
                        </Link>
                        <span className="text-xs text-[#F2C1AE]/50">{notification.time}</span>
                      </div>
                      {!notification.read && <div className="h-2 w-2 rounded-full bg-[#F24F13]" />}
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </main>

        {/* Mobile new post button */}
        <div className="md:hidden fixed bottom-4 right-4">
          <Button className="h-12 w-12 rounded-full bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white shadow-lg">
            <Plus className="h-6 w-6" />
          </Button>
        </div>
        {/* Add this right before the final closing div */}
        <Footer />
      </div>
    </div>
  )
}
