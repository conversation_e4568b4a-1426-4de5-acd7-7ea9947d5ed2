"use client"

import { ProtectedRoute } from '@/components/protected-route'
import { useAuth } from '@/contexts/auth-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { ArrowLeft, User, Mail, Calendar } from 'lucide-react'

export default function ProfilePage() {
  return (
    <ProtectedRoute>
      <ProfileContent />
    </ProtectedRoute>
  )
}

function ProfileContent() {
  const { user, logout } = useAuth()

  if (!user) return null

  return (
    <div className="min-h-screen bg-[#1e2a36] text-white">
      <header className="sticky top-0 z-10 bg-[#1e2a36]/80 backdrop-blur-md border-b border-[#2a3a4a] p-4 flex justify-between items-center">
        <Link href="/" className="flex items-center space-x-2">
          <ArrowLeft className="h-5 w-5 text-[#F2C1AE]/70" />
          <span className="text-[#F2C1AE]/70">Back to Forum</span>
        </Link>
        <Button
          variant="outline"
          onClick={logout}
          className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
        >
          Logout
        </Button>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-2xl">
        <Card className="bg-[#2a3a4a]/60 border-[#2a3a4a]">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Avatar className="h-20 w-20 border-2 border-[#F26430]">
                <AvatarFallback className="text-2xl bg-[#F26430] text-white">
                  {user.name[0].toUpperCase()}
                </AvatarFallback>
              </Avatar>
            </div>
            <CardTitle className="text-2xl bg-gradient-to-r from-[#F24F13] to-[#F26430] bg-clip-text text-transparent">
              User Profile
            </CardTitle>
            <CardDescription className="text-[#F2C1AE]/70">
              Your account information and settings
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="grid gap-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-[#1e2a36]/50">
                <User className="h-5 w-5 text-[#F26430]" />
                <div>
                  <p className="text-sm text-[#F2C1AE]/70">Name</p>
                  <p className="font-medium text-white">{user.name}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-[#1e2a36]/50">
                <Mail className="h-5 w-5 text-[#F26430]" />
                <div>
                  <p className="text-sm text-[#F2C1AE]/70">Email</p>
                  <p className="font-medium text-white">{user.email}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-[#1e2a36]/50">
                <Calendar className="h-5 w-5 text-[#F26430]" />
                <div>
                  <p className="text-sm text-[#F2C1AE]/70">User ID</p>
                  <p className="font-mono text-sm text-white">{user.user_id}</p>
                </div>
              </div>
            </div>

            <div className="pt-4 border-t border-[#2a3a4a]">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-white">Account Status</h3>
                  <p className="text-sm text-[#F2C1AE]/70">Your account is active and verified</p>
                </div>
                <Badge className="bg-green-500/20 text-green-400 border-green-500/50">
                  Active
                </Badge>
              </div>
            </div>

            <div className="pt-4 space-y-3">
              <h3 className="font-medium text-white">Quick Actions</h3>
              <div className="grid gap-2">
                <Link href="/">
                  <Button 
                    variant="outline" 
                    className="w-full justify-start border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
                  >
                    Browse Forum
                  </Button>
                </Link>
                <Button 
                  variant="outline" 
                  className="w-full justify-start border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
                  disabled
                >
                  Edit Profile (Coming Soon)
                </Button>
                <Button 
                  variant="outline" 
                  onClick={logout}
                  className="w-full justify-start border-red-500/50 text-red-400 hover:text-red-300 hover:bg-red-500/10"
                >
                  Sign Out
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
