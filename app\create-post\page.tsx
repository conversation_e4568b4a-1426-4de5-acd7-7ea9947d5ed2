"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ArrowLeft, Loader2, AlertCircle, Send } from "lucide-react"
import { ProtectedRoute } from "@/components/protected-route"
import { useAuth } from "@/contexts/auth-context"
import { categoriesApi, postsApi, generateExcerpt, type Category } from "@/lib/database-service"

export default function CreatePostPage() {
  return (
    <ProtectedRoute>
      <CreatePostContent />
    </ProtectedRoute>
  )
}

function CreatePostContent() {
  const [title, setTitle] = useState("")
  const [content, setContent] = useState("")
  const [categoryId, setCategoryId] = useState("")
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingCategories, setIsLoadingCategories] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const { user } = useAuth()
  const router = useRouter()

  // Load categories on mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const response = await categoriesApi.getAll()
        setCategories(response.data)
      } catch (err) {
        console.error('Failed to load categories:', err)
        setError('Failed to load categories. Please refresh the page.')
      } finally {
        setIsLoadingCategories(false)
      }
    }

    loadCategories()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) {
      setError('You must be logged in to create a post')
      return
    }

    if (!title.trim()) {
      setError('Please enter a title')
      return
    }

    if (!content.trim()) {
      setError('Please enter some content')
      return
    }

    if (!categoryId) {
      setError('Please select a category')
      return
    }

    setIsLoading(true)
    setError("")
    setSuccess("")

    try {
      const excerpt = generateExcerpt(content, 200)
      
      const newPost = {
        title: title.trim(),
        content: content.trim(),
        excerpt,
        author_id: user.user_id,
        category_id: categoryId,
        pinned: false,
      }

      const createdPosts = await postsApi.create(newPost)
      
      if (createdPosts && createdPosts.length > 0) {
        setSuccess('Post created successfully! Redirecting...')
        setTimeout(() => {
          router.push(`/thread/${createdPosts[0].id}`)
        }, 1500)
      } else {
        throw new Error('Failed to create post')
      }
    } catch (err) {
      console.error('Failed to create post:', err)
      setError(err instanceof Error ? err.message : 'Failed to create post. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-[#1e2a36] text-white">
      <header className="sticky top-0 z-10 bg-[#1e2a36]/80 backdrop-blur-md border-b border-[#2a3a4a] p-4 flex justify-between items-center">
        <Link href="/" className="flex items-center space-x-2">
          <ArrowLeft className="h-5 w-5 text-[#F2C1AE]/70" />
          <span className="text-[#F2C1AE]/70">Back to Forum</span>
        </Link>
        <div className="text-sm text-[#F2C1AE]/70">
          {user && `Creating as ${user.name}`}
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <Card className="bg-[#2a3a4a]/60 border-[#2a3a4a]">
          <CardHeader>
            <CardTitle className="text-2xl bg-gradient-to-r from-[#F24F13] to-[#F26430] bg-clip-text text-transparent">
              Create New Post
            </CardTitle>
            <CardDescription className="text-[#F2C1AE]/70">
              Share your thoughts with the community
            </CardDescription>
          </CardHeader>

          <CardContent>
            {/* Error/Success Messages */}
            {error && (
              <Alert className="mb-6 border-red-500/50 bg-red-500/10">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <AlertDescription className="text-red-400">{error}</AlertDescription>
              </Alert>
            )}
            {success && (
              <Alert className="mb-6 border-green-500/50 bg-green-500/10">
                <AlertCircle className="h-4 w-4 text-green-500" />
                <AlertDescription className="text-green-400">{success}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Category Selection */}
              <div className="space-y-2">
                <Label htmlFor="category" className="text-[#F2C1AE]/70">
                  Category *
                </Label>
                {isLoadingCategories ? (
                  <div className="flex items-center space-x-2 p-3 bg-[#1e2a36]/50 rounded-md">
                    <Loader2 className="h-4 w-4 animate-spin text-[#F26430]" />
                    <span className="text-[#F2C1AE]/70">Loading categories...</span>
                  </div>
                ) : (
                  <Select value={categoryId} onValueChange={setCategoryId} disabled={isLoading}>
                    <SelectTrigger className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430]">
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#2a3a4a] border-[#2a3a4a]">
                      {categories.map((category) => (
                        <SelectItem 
                          key={category.id} 
                          value={category.id}
                          className="text-white hover:bg-[#1e2a36] focus:bg-[#1e2a36]"
                        >
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title" className="text-[#F2C1AE]/70">
                  Title *
                </Label>
                <Input
                  id="title"
                  type="text"
                  placeholder="Enter your post title..."
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430]"
                  disabled={isLoading}
                  maxLength={200}
                />
                <div className="text-xs text-[#F2C1AE]/50 text-right">
                  {title.length}/200 characters
                </div>
              </div>

              {/* Content */}
              <div className="space-y-2">
                <Label htmlFor="content" className="text-[#F2C1AE]/70">
                  Content *
                </Label>
                <Textarea
                  id="content"
                  placeholder="Write your post content here..."
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430] min-h-[200px] resize-y"
                  disabled={isLoading}
                />
                <div className="text-xs text-[#F2C1AE]/50 text-right">
                  {content.length} characters
                </div>
              </div>

              {/* Preview */}
              {content.trim() && (
                <div className="space-y-2">
                  <Label className="text-[#F2C1AE]/70">Preview</Label>
                  <div className="p-4 bg-[#1e2a36]/50 border border-[#2a3a4a] rounded-md">
                    <h3 className="font-medium text-white mb-2">{title || "Untitled Post"}</h3>
                    <div className="text-[#F2C1AE]/80 whitespace-pre-wrap text-sm">
                      {generateExcerpt(content, 200)}
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <div className="flex justify-end space-x-3 pt-4">
                <Link href="/">
                  <Button
                    type="button"
                    variant="outline"
                    className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]"
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                </Link>
                <Button
                  type="submit"
                  disabled={isLoading || !title.trim() || !content.trim() || !categoryId}
                  className="bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white disabled:opacity-50"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Create Post
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
