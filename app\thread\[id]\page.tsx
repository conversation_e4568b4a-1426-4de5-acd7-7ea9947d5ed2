"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  MessageSquare,
  Send,
  Loader2,
  AlertCircle,
  Reply
} from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import {
  postsApi,
  commentsApi,
  categoriesApi,
  usersApi,
  votesApi,
  formatDate,
  type Post,
  type Comment,
  type Category,
  type User as DbUser
} from "@/lib/database-service"

export default function ThreadPage() {
  const params = useParams()
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const postId = params.id as string
  // State
  const [post, setPost] = useState<Post | null>(null)
  const [comments, setComments] = useState<Comment[]>([])
  const [category, setCategory] = useState<Category | null>(null)
  const [author, setAuthor] = useState<DbUser | null>(null)
  const [commentAuthors, setCommentAuthors] = useState<Record<string, DbUser>>({})
  const [postVotes, setPostVotes] = useState({ upvotes: 0, downvotes: 0, userVote: undefined as 'upvote' | 'downvote' | undefined })
  const [commentVotes, setCommentVotes] = useState<Record<string, { upvotes: number; downvotes: number; userVote?: 'upvote' | 'downvote' }>>({})

  // Form state
  const [newComment, setNewComment] = useState("")
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState("")

  // Loading states
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)
  const [error, setError] = useState("")

  // Load post data
  useEffect(() => {
    if (postId) {
      loadPostData()
    }
  }, [postId])

  // Load votes when user changes
  useEffect(() => {
    if (post) {
      loadPostVotes()
    }
    if (comments.length > 0) {
      loadCommentVotes()
    }
  }, [user, post, comments.length])

  const loadPostData = async () => {
    try {
      setIsLoading(true)
      setError("")

      // Load post
      const postData = await postsApi.getById(postId)
      setPost(postData)

      // Load category
      const categoryData = await categoriesApi.getById(postData.category_id)
      setCategory(categoryData)

      // Load author
      const authorData = await usersApi.getById(postData.author_id)
      setAuthor(authorData)

      // Load comments
      const commentsResponse = await commentsApi.getByPostId(postId, { order: 'created_at:asc' })
      setComments(commentsResponse.data)

      // Load comment authors
      const authorIds = [...new Set(commentsResponse.data.map(comment => comment.author_id))]
      const authors: Record<string, DbUser> = {}
      await Promise.all(
        authorIds.map(async (authorId) => {
          try {
            const author = await usersApi.getById(authorId)
            authors[authorId] = author
          } catch (err) {
            console.error(`Failed to load author ${authorId}:`, err)
          }
        })
      )
      setCommentAuthors(authors)

    } catch (err) {
      console.error('Failed to load post:', err)
      setError('Failed to load post. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const loadPostVotes = async () => {
    if (!post) return

    try {
      const votes = await votesApi.getPostVotes(post.id)
      const upvotes = votes.data.filter(vote => vote.vote_type === 'upvote').length
      const downvotes = votes.data.filter(vote => vote.vote_type === 'downvote').length

      let userVote: 'upvote' | 'downvote' | undefined
      if (user) {
        const userVoteData = votes.data.find(vote => vote.user_id === user.user_id)
        userVote = userVoteData?.vote_type as 'upvote' | 'downvote' | undefined
      }

      setPostVotes({ upvotes, downvotes, userVote })
    } catch (error) {
      console.error('Failed to load post votes:', error)
    }
  }

  const loadCommentVotes = async () => {
    try {
      const votePromises = comments.map(async (comment) => {
        const votes = await votesApi.getCommentVotes(comment.id)
        const upvotes = votes.data.filter(vote => vote.vote_type === 'upvote').length
        const downvotes = votes.data.filter(vote => vote.vote_type === 'downvote').length

        let userVote: 'upvote' | 'downvote' | undefined
        if (user) {
          const userVoteData = votes.data.find(vote => vote.user_id === user.user_id)
          userVote = userVoteData?.vote_type as 'upvote' | 'downvote' | undefined
        }

        return { commentId: comment.id, upvotes, downvotes, userVote }
      })

      const voteResults = await Promise.all(votePromises)
      const voteMap: Record<string, { upvotes: number; downvotes: number; userVote?: 'upvote' | 'downvote' }> = {}

      voteResults.forEach(({ commentId, upvotes, downvotes, userVote }) => {
        voteMap[commentId] = { upvotes, downvotes, userVote }
      })

      setCommentVotes(voteMap)
    } catch (error) {
      console.error('Failed to load comment votes:', error)
    }
  }

  const handlePostVote = async (voteType: 'upvote' | 'downvote') => {
    if (!user || !post) return

    try {
      const currentVote = postVotes.userVote

      if (currentVote === voteType) {
        // Remove vote
        const existingVotes = await votesApi.getPostVotes(post.id)
        const userVote = existingVotes.data.find(vote => vote.user_id === user.user_id)
        if (userVote) {
          await votesApi.delete(userVote.id)
        }

        setPostVotes(prev => ({
          ...prev,
          [voteType === 'upvote' ? 'upvotes' : 'downvotes']: Math.max(0, prev[voteType === 'upvote' ? 'upvotes' : 'downvotes'] - 1),
          userVote: undefined
        }))
      } else {
        // Add or change vote
        await votesApi.vote({
          user_id: user.user_id,
          post_id: post.id,
          vote_type: voteType
        })

        setPostVotes(prev => {
          const newVotes = { ...prev }

          // Remove previous vote if exists
          if (currentVote) {
            newVotes[currentVote === 'upvote' ? 'upvotes' : 'downvotes'] = Math.max(0, newVotes[currentVote === 'upvote' ? 'upvotes' : 'downvotes'] - 1)
          }

          // Add new vote
          newVotes[voteType === 'upvote' ? 'upvotes' : 'downvotes'] += 1
          newVotes.userVote = voteType

          return newVotes
        })
      }
    } catch (error) {
      console.error('Failed to vote on post:', error)
    }
  }

  const handleCommentSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user || !post) return
    if (!newComment.trim()) return

    try {
      setIsSubmittingComment(true)
      setError("")

      const comment = {
        content: newComment.trim(),
        author_id: user.user_id,
        post_id: post.id,
      }

      const createdComments = await commentsApi.create(comment)

      if (createdComments && createdComments.length > 0) {
        // Reload comments to get the latest data
        await loadPostData()
        setNewComment("")
      }
    } catch (err) {
      console.error('Failed to create comment:', err)
      setError('Failed to post comment. Please try again.')
    } finally {
      setIsSubmittingComment(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-[#1e2a36]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-[#F26430]" />
          <p className="text-[#F2C1AE]/70">Loading post...</p>
        </div>
      </div>
    )
  }

  if (error && !post) {
    return (
      <div className="min-h-screen bg-[#1e2a36] text-white p-4">
        <div className="max-w-2xl mx-auto">
          <Alert className="border-red-500/50 bg-red-500/10">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-400">{error}</AlertDescription>
          </Alert>
          <div className="mt-4">
            <Link href="/">
              <Button variant="outline" className="border-[#2a3a4a] text-[#F2C1AE]/80 hover:text-[#F2C1AE] hover:bg-[#2a3a4a]">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Forum
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  if (!post) return null

  return (
    <div className="min-h-screen bg-[#1e2a36] text-white">
      <header className="sticky top-0 z-10 bg-[#1e2a36]/80 backdrop-blur-md border-b border-[#2a3a4a] p-4 flex justify-between items-center">
        <Link href="/" className="flex items-center space-x-2">
          <ArrowLeft className="h-5 w-5 text-[#F2C1AE]/70" />
          <span className="text-[#F2C1AE]/70">Back to Forum</span>
        </Link>
        {category && (
          <Badge className="bg-[#F26430]/20 text-[#F26430]">
            {category.name}
          </Badge>
        )}
      </header>

      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Post Content */}
        <Card className="bg-[#2a3a4a]/60 border-[#2a3a4a] mb-8">
          <div className="flex">
            {/* Voting */}
            <div className="flex flex-col items-center p-6 border-r border-[#2a3a4a] text-[#F2C1AE]/70">
              <button
                className={`hover:text-[#F24F13] transition-colors ${postVotes.userVote === 'upvote' ? 'text-[#F24F13]' : ''}`}
                onClick={() => handlePostVote('upvote')}
                disabled={!isAuthenticated}
              >
                <ArrowUp className="h-6 w-6" />
              </button>
              <span className="my-2 font-medium text-xl">{postVotes.upvotes - postVotes.downvotes}</span>
              <button
                className={`hover:text-[#F26430] transition-colors ${postVotes.userVote === 'downvote' ? 'text-[#F26430]' : ''}`}
                onClick={() => handlePostVote('downvote')}
                disabled={!isAuthenticated}
              >
                <ArrowDown className="h-6 w-6" />
              </button>
            </div>

            {/* Content */}
            <div className="flex-1">
              <CardHeader>
                <div className="flex flex-wrap items-center gap-2 mb-2">
                  {post.pinned && (
                    <Badge variant="outline" className="text-xs font-normal border-[#2a3a4a] text-[#F2C1AE]/70">
                      Pinned
                    </Badge>
                  )}
                  {category && (
                    <Badge className="text-xs font-normal bg-[#F26430]/20 text-[#F26430]">
                      {category.name}
                    </Badge>
                  )}
                </div>

                <h1 className="text-2xl font-bold text-white mb-4">{post.title}</h1>

                <div className="flex items-center gap-3 text-sm text-[#F2C1AE]/70">
                  <Avatar className="h-6 w-6 border border-[#2a3a4a]">
                    <AvatarFallback className="text-xs bg-[#F26430] text-white">
                      {author?.name?.[0]?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <span>{author?.name || 'Unknown User'}</span>
                  <span>•</span>
                  <span>{formatDate(post.created_at)}</span>
                  <span>•</span>
                  <div className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-1" />
                    <span>{comments.length} comments</span>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="prose prose-invert max-w-none">
                  <p className="text-[#F2C1AE] whitespace-pre-wrap leading-relaxed">{post.content}</p>
                </div>
              </CardContent>
            </div>
          </div>
        </Card>

        {/* Error Message */}
        {error && (
          <Alert className="mb-6 border-red-500/50 bg-red-500/10">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-400">{error}</AlertDescription>
          </Alert>
        )}

        {/* Comment Form */}
        {isAuthenticated ? (
          <Card className="bg-[#2a3a4a]/60 border-[#2a3a4a] mb-8">
            <CardHeader>
              <h3 className="text-lg font-medium text-white">Add a Comment</h3>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCommentSubmit} className="space-y-4">
                <Textarea
                  placeholder="Write your comment here..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="bg-[#1e2a36]/50 border-[#2a3a4a] text-white focus:border-[#F26430] min-h-[100px] resize-y"
                  disabled={isSubmittingComment}
                />
                <div className="flex justify-end">
                  <Button
                    type="submit"
                    disabled={isSubmittingComment || !newComment.trim()}
                    className="bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white disabled:opacity-50"
                  >
                    {isSubmittingComment ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Posting...
                      </>
                    ) : (
                      <>
                        <Send className="mr-2 h-4 w-4" />
                        Post Comment
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        ) : (
          <Card className="bg-[#2a3a4a]/60 border-[#2a3a4a] mb-8">
            <CardContent className="text-center py-8">
              <p className="text-[#F2C1AE]/70 mb-4">Please log in to join the discussion</p>
              <Link href="/login">
                <Button className="bg-gradient-to-r from-[#F24F13] to-[#F26430] hover:from-[#F26430] hover:to-[#F24F13] text-white">
                  Login to Comment
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Comments */}
        <div className="space-y-4">
          <h3 className="text-xl font-bold text-white">
            Comments ({comments.length})
          </h3>

          {comments.length === 0 ? (
            <Card className="bg-[#2a3a4a]/60 border-[#2a3a4a]">
              <CardContent className="text-center py-8">
                <MessageSquare className="h-12 w-12 mx-auto text-[#F2C1AE]/50 mb-4" />
                <p className="text-[#F2C1AE]/70">No comments yet. Be the first to comment!</p>
              </CardContent>
            </Card>
          ) : (
            comments.map((comment) => {
              const commentAuthor = commentAuthors[comment.author_id]
              const votes = commentVotes[comment.id] || { upvotes: 0, downvotes: 0 }

              return (
                <Card key={comment.id} className="bg-[#2a3a4a]/60 border-[#2a3a4a]">
                  <div className="flex">
                    {/* Comment Voting */}
                    <div className="flex flex-col items-center p-4 border-r border-[#2a3a4a] text-[#F2C1AE]/70">
                      <button
                        className={`hover:text-[#F24F13] transition-colors ${votes.userVote === 'upvote' ? 'text-[#F24F13]' : ''}`}
                        disabled={!isAuthenticated}
                      >
                        <ArrowUp className="h-4 w-4" />
                      </button>
                      <span className="my-1 font-medium text-sm">{votes.upvotes - votes.downvotes}</span>
                      <button
                        className={`hover:text-[#F26430] transition-colors ${votes.userVote === 'downvote' ? 'text-[#F26430]' : ''}`}
                        disabled={!isAuthenticated}
                      >
                        <ArrowDown className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Comment Content */}
                    <div className="flex-1">
                      <CardHeader className="pb-2">
                        <div className="flex items-center gap-2 text-sm text-[#F2C1AE]/70">
                          <Avatar className="h-5 w-5 border border-[#2a3a4a]">
                            <AvatarFallback className="text-xs bg-[#F26430] text-white">
                              {commentAuthor?.name?.[0]?.toUpperCase() || 'U'}
                            </AvatarFallback>
                          </Avatar>
                          <span>{commentAuthor?.name || 'Unknown User'}</span>
                          <span>•</span>
                          <span>{formatDate(comment.created_at)}</span>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-[#F2C1AE] whitespace-pre-wrap">{comment.content}</p>
                      </CardContent>
                    </div>
                  </div>
                </Card>
              )
            })
          )}
        </div>
      </main>
    </div>
  )
}
